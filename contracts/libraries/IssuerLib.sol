// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;

import "../interfaces/IContractManager.sol";
import "../interfaces/Struct.sol";
import "../interfaces/Error.sol";

library IssuerLib {
    /** @dev 検索結果の最大取得件数 */
    uint256 private constant _MAX_LIMIT = 100;
    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant _EMPTY_LENGTH = 0;

    /**
     * @dev 発行者の権限を取得する
     *
     * @param issuerMapping 発行者データを保存するマッピング
     * @param key マッピングのキーとなる発行者ID
     * @return role 発行者IDに紐づく権限
     */
    function getIssuerRole(mapping(bytes32 => IssuerData) storage issuerMapping, bytes32 key)
        external
        view
        returns (bytes32 role)
    {
        return issuerMapping[key].role;
    }

    /**
     * @dev 発行者IDを取得する
     *
     * @param issuerIdMapping 発行者IDリストを保存するマッピング
     * @param key マッピングのキーとなる配列のindex
     * @return issuerId 発行者ID
     * @return err エラーメッセージ
     */
    function getIssuerId(bytes32[] storage issuerIdMapping, uint256 key)
        external
        view
        returns (bytes32 issuerId, string memory err)
    {
        if (issuerIdMapping.length <= key) {
            return (0x00, Error.ISSUER_OUT_OF_INDEX);
        }
        return (issuerIdMapping[key], "");
    }

    /**
     * @dev 発行者データを追加する
     *
     * @param issuerMapping 発行者データを保存するマッピング
     * @param key マッピングのキーとなる発行者ID
     * @param role 発行者の権限
     * @param name 発行者の名前
     */
    function addIssuer(
        mapping(bytes32 => IssuerData) storage issuerMapping,
        bytes32 key,
        bytes32 role,
        string memory name,
        uint16 bankCode
    ) external {
        issuerMapping[key].role = role;
        issuerMapping[key].name = name;
        issuerMapping[key].bankCode = bankCode;
    }

    /**
     * @dev 発行者IDの追加
     *
     * @param issuerIdMapping 発行者データを保存するマッピング
     * @param key 発行者ID
     * @param issuerIdExistence 発行者IDが登録済フラグ
     */
    function addIssuerId(
        bytes32[] storage issuerIdMapping,
        bytes32 key,
        bool issuerIdExistence
    ) external {
        // issuerID未登録チェック
        {
            require(key != 0x00, Error.ISSUER_INVALID_VAL);
            require(!issuerIdExistence, Error.ISSUER_ID_EXIST);
        }
        issuerIdMapping.push(key);
    }

    /**
     * @dev アカウントIDを追加する
     *
     * @param issuerMapping 発行者データを保存するマッピング
     * @param key マッピングのキーとなる発行者ID
     * @param accountId アカウントのID
     */
    function addAccountId(
        mapping(bytes32 => IssuerData) storage issuerMapping,
        bytes32 key,
        bytes32 accountId
    ) external {
        issuerMapping[key].accountIds.push(accountId);
    }

    /**
     * @dev 発行者データを変更する
     *
     * @param issuerMapping 発行者データを保存するマッピング
     * @param key マッピングのキーとなる発行者ID
     * @param name 発行者の名前
     */
    function modIssuer(
        mapping(bytes32 => IssuerData) storage issuerMapping,
        bytes32 key,
        string memory name
    ) external {
        if (bytes(name).length > 0) {
            issuerMapping[key].name = name;
        }
    }

    /**
     * @dev 発行者の存在確認。
     *
     * @param issuerId チェック対象となる発行者Id
     * @param issuerIdExistence 発行者IDが登録済フラグ
     * @return success true:存在する,false:存在しない
     * @return err エラーメッセージ
     */
    function hasIssuer(bytes32 issuerId, bool issuerIdExistence)
        external
        pure
        returns (bool success, string memory err)
    {
        if (issuerId == 0x00) {
            return (false, Error.ISSUER_INVALID_VAL);
        }
        if (!issuerIdExistence) {
            return (false, Error.ISSUER_ID_NOT_EXIST);
        }
        return (true, "");
    }

    /**
     * @dev 指定されたIssuerIDにAccountが紐付いているか確認する
     *
     * @param accountId チェック対象となるアカウントID
     * @param accountIdExistenceByIssuerId  アカウントIDが発行者IDに紐付き済フラグ
     * @return success true:紐づいている,false:紐づいていない
     * @return err エラーメッセージ
     */
    function hasAccount(bytes32 accountId, bool accountIdExistenceByIssuerId)
        internal
        pure
        returns (bool success, string memory err)
    {
        // AccountID入力確認
        if (accountId == 0x00) {
            return (false, Error.INVALID_ACCOUNT_ID);
        }
        // Account存在確認
        if (!accountIdExistenceByIssuerId) {
            return (false, Error.ACCOUNT_ID_NOT_EXIST);
        }
        return (true, "");
    }
}
