// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.10;
pragma abicoder v2;

import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";

import "./interfaces/IContractManager.sol";
import "./ContractManager.sol";

// ライブラリのimport
import "./libraries/ValidatorLib.sol";
import "./remigration/RemigrationLib.sol";
import "./interfaces/Struct.sol";
import "./interfaces/Error.sol";

/**
 * @dev Validatorコントラクト
 */
contract Validator is Initializable, IValidator {
    ///////////////////////////////////
    // private variables
    ///////////////////////////////////

    using ValidatorLib for *;
    using RemigrationLib for *;

    /** @dev ContractManagerアドレス **/
    IContractManager private _contractManager;
    /** @dev 検証者ID */
    bytes32[] private _validatorIds;
    /** @dev 検証者IDの存在確認フラグ(validatorId => boolean) */
    mapping(bytes32 => bool) private _validatorIdExistence;
    /** @dev 検証者IDに紐づくアカウントIdの存在確認フラグ(validatorId => accountId => boolean) */
    mapping(bytes32 => mapping(bytes32 => bool)) private _accountIdExistenceByValidatorId;
    /** @dev 発行者IDの紐付けフラグ(issuerId => boolean) */
    mapping(bytes32 => bool) private _issuerIdLinkedFlag;
    /** @dev 検証者データ */
    mapping(bytes32 => ValidatorData) private _validatorData;
    /** @dev 検索結果の最大取得件数 */
    uint256 private constant _MAX_LIMIT = 100;
    /** @dev 共通領域のID **/
    uint16 private constant _FINANCIAL_ZONE = 3000;
    /** @dev 未登録の場合にて返す空の件数 **/
    uint256 private constant _EMPTY_LENGTH = 0;
    /** @dev 未登録の場合にて返す空のリスト */
    uint256[] _EMPTY_LIST = new uint256[](0);
    /** @dev  外部署名の検証に使用する固定値*/
    bytes32 private constant _STRING_TERMINATE = "terminate";
    /** @dev GET ALL関数の最大取得件数 */
    uint256 private constant _GET_VALIDATORS_LIMIT = 1000;
    /** @dev setValidatorsAllのsignature検証用 */
    string private constant _SET_VALIDATORS_ALL_SIGNATURE = "setValidatorsAll";
    /// @dev Validatorロール計算用(calcRole()のprefix用文字列(Validator権限))
    bytes32 public constant ROLE_PREFIX_VALIDATOR = keccak256("VALIDATOR_ROLE");

    /** @dev getValidatorsAllのsignature検証用 **/
    // string private constant _GET_VALIDATORS_ALL_SIGNATURE = "getValidatorsAll";
    /* @dev setValidatorsAllのsignature検証用 */
    // string private constant _SET_VALIDATORS_ALL_SIGNATURE = "setValidatorsAll";

    ///////////////////////////////////
    // functions
    ///////////////////////////////////

    /**
     * @dev Upgrades Pluginsでのdeploy後に呼び出される。通常のコンストラクタ相当。
     *
     * @param contractManager ContractManagerアドレス
     */
    function initialize(IContractManager contractManager) public initializer {
        _contractManager = contractManager;
    }

    /**
     * @dev コントラクトバージョン取得。
     *
     * @return コントラクトバージョン
     */
    function version() external pure returns (string memory) {
        return "v1";
    }

    ///////////////////////////////////
    // verify sender functions
    ///////////////////////////////////

    /**
     * @dev Admin権限を持つかチェックする。署名からEOAを復元し権限を持つかチェックする(内部関数)。
     *
     * @param hash signatureの計算元ハッシュ値
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature 権限チェック対象の署名
     */
    function _adminOnly(
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) internal view {
        (bool has, string memory err) = _contractManager.accessCtrl().checkAdminRole(
            hash,
            deadline,
            signature
        );
        require(bytes(err).length == 0, err);
        require(has, Error.VALIDATOR_NOT_ADMIN_ROLE);
    }

    ///////////////////////////////////
    // send functions
    ///////////////////////////////////

    /**
     * @dev Validatorの追加。Adminの権限が必要。
     *
     * ```
     * emit event: AddValidator()
     * ```
     *
     * @param validatorId validatorId
     * @param issuerId issuerId
     * @param name validator名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function addValidator(
        bytes32 validatorId,
        bytes32 issuerId,
        bytes32 name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(validatorId, issuerId, name, deadline));
            _adminOnly(hash, deadline, signature);
        }

        ValidatorLib.addValidator(
            _validatorData,
            _validatorIdExistence,
            _issuerIdLinkedFlag,
            _contractManager,
            validatorId,
            name,
            issuerId
        );
        //ValidatorIdを登録する
        _validatorIds.push(validatorId);

        emit AddValidator(validatorId, issuerId, name, traceId);
    }

    /**
     * @dev Accountを登録する(共通領域)。
     *
     * ```
     * emit event: AddAccount()
     * ```
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @param accountName account名
     * @param limitAmounts 限度額の配列
     * @param traceId トレースID
     */
    function addAccount(
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        uint256[] memory limitAmounts,
        bytes32 traceId
    ) external override {
        ValidatorLib.addAccount(
            _validatorData,
            _accountIdExistenceByValidatorId,
            _contractManager,
            validatorId,
            accountId,
            accountName,
            limitAmounts,
            traceId
        );

        // Depositedが有効である場合はアカウント作成時に本人確認をFalseに設定する。
        // bool identified = !_contractManager.token().isDeposited();
        // TODO どうするか確認
        bool identified = false;
        bool enabled = true;

        // emit event
        emit AddAccount(validatorId, accountId, identified, enabled, limitAmounts, traceId);
    }

    /**
     * @dev バリデータが直接管理するアカウントIDを追加する
     *
     * @param validatorId バリデータID
     * @param accountId アカウントID
     */
    function addValidatorAccountId(
        bytes32 validatorId,
        bytes32 accountId,
        bytes32 traceId
    ) external override {
        _validatorData.addValidatorAccountId(
            _accountIdExistenceByValidatorId,
            _contractManager,
            validatorId,
            accountId
        );

        emit AddValidatorAccountId(validatorId, accountId, traceId);
    }

    /**
     * @dev BusinessZoneAccountを追加する
     *
     */
    function setActiveBusinessAccountWithZone(
        bytes32 validatorId,
        uint16 zoneId,
        bytes32 accountId,
        bytes32 traceId
    ) external override {
        {
            // Validator存在確認
            (bool success, string memory err) = _validatorIdExistence.hasValidator(validatorId);
            require(success, err);
        }
        {
            // ValidatorとAccountの紐付き確認
            (bool success, string memory errTmp) = _hasAccount(validatorId, accountId);
            require(success, errTmp);
        }
        // AccountコントラクトのsetActiveBusinessAccountWithZoneを呼び出す
        _contractManager.businessZoneAccount().setActiveBusinessAccountWithZone(
            zoneId,
            accountId,
            traceId
        );
    }

    /**
     * @dev BusinessZoneアカウント解約
     *
     * ```
     * emit event: SetBizZoneTerminated()
     * ```
     *
     * @param zoneId ゾーンID
     * @param accountId アカウントID
     * @param traceId トレースID
     */
    function setBizZoneTerminated(
        uint16 zoneId,
        bytes32 accountId,
        bytes32 traceId
    ) external override {
        _contractManager.businessZoneAccount().setBizZoneTerminated(zoneId, accountId);

        //Accountに紐づくvalidatorIdを取得
        (bytes32 validatorId, ) = _contractManager.account().getValidatorIdByAccountId(accountId);

        // emit event
        emit SetBizZoneTerminated(validatorId, zoneId, accountId, traceId);
    }

    /**
     * @dev BizZone向けAccount登録。(CoreAPI/synchronous用)
     *
     * ```
     * emit event: SyncAccount()
     * ```
     *
     * @dev Account登録（付加領域用)。
     * @param validatorId validatorId
     * @param accountId accountId
     * @param zoneId zoneId
     * @param zoneName zoneName
     * @param accountStatus 口座のステータス
     * @param approvalAmount 承認額
     * @param traceId トレースID
     */
    function syncAccount(
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        uint16 zoneId,
        string memory zoneName,
        bytes32 accountStatus,
        bytes32 reasonCode,
        uint256 approvalAmount,
        bytes32 traceId
    ) external override {
        ValidatorLib.syncAccount(
            _validatorData,
            _accountIdExistenceByValidatorId,
            _contractManager,
            validatorId,
            accountId,
            accountName,
            accountStatus,
            reasonCode,
            traceId
        );

        // 承認額が0より大きい場合は、validatorAccountIdをspenderとして、承認操作を行う
        if (accountStatus == Constant._STATUS_APPLIYNG && approvalAmount > 0) {
            bytes32 validatorAccountId;
            string memory err;
            (validatorAccountId, err) = _contractManager.validator().getValidatorAccountId(
                validatorId
            );
            require(bytes(err).length == 0, err);
            _contractManager.token().approve(
                validatorId,
                accountId,
                validatorAccountId,
                approvalAmount,
                traceId
            );
        }

        // emit event
        emit SyncAccount(
            validatorId,
            accountId,
            accountName,
            zoneId,
            zoneName,
            accountStatus,
            approvalAmount,
            traceId
        );
    }

    /**
     * @dev Accountを解約する。
     *
     * ```
     * emit event: SetTerminated()
     * ```
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @param reasonCode 理由コード
     * @param traceId トレースID
     */
    function setTerminated(
        bytes32 validatorId,
        bytes32 accountId,
        bytes32 reasonCode,
        bytes32 traceId
    ) external override {
        {
            // ValidatorIdの存在チェック確認
            (bool success, string memory errTmp) = _validatorIdExistence.hasValidator(validatorId);
            require(success, errTmp);
        }
        {
            // ValidatorとAccountの紐付き確認
            (bool success, string memory errTmp) = _hasAccount(validatorId, accountId);
            require(success, errTmp);
        }

        // Accountの解約フラグを更新する
        _contractManager.account().setTerminated(accountId, reasonCode, traceId);

        // Web3Streamで残高管理と同期を行う為、Eventで出力するzoneIdを取得する。
        (uint16 zoneId, , string memory errZone) = _contractManager.provider().getZone();
        require(bytes(errZone).length == 0, errZone);

        // EventをEmit
        emit SetTerminated(zoneId, accountId, reasonCode, traceId);
    }

    /**
     * @dev Accountを解約する。Adminの権限が必要。
     *
     * ```
     * emit event: AddValidatorRole()
     * ```
     *
     * @param validatorId validatorId
     * @param validatorEoa validatorEoa
     * @param deadline signatureのタイムスタンプ(秒)
     * @param traceId トレースID
     * @param signature Adminユーザによる署名
     */
    function addValidatorRole(
        bytes32 validatorId,
        address validatorEoa,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(validatorId, validatorEoa, deadline));
            _adminOnly(hash, deadline, signature);
        }

        // validatorId有効性チェック
        {
            (bool success, string memory errTmp) = _validatorIdExistence.hasValidator(validatorId);
            require(success, errTmp);
        }

        require(validatorEoa != address(0), Error.VALIDATOR_INVALID_VAL);
        bytes32 role = _contractManager.accessCtrl().calcRole(ROLE_PREFIX_VALIDATOR, validatorId);
        _validatorData.addValidatorRole(validatorId, role, true);

        _contractManager.accessCtrl().addRoleByValidator(validatorId, role, validatorEoa);

        emit AddValidatorRole(validatorId, validatorEoa, traceId);
    }

    /**
     * @dev 検証者の名前を更新する。Adminの権限が必要。
     *
     * ```
     * emit event: ModValidator()
     * ```
     *
     * @param validatorId validatorId
     * @param name validator名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function modValidator(
        bytes32 validatorId,
        bytes32 name,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(validatorId, name, deadline));
            _adminOnly(hash, deadline, signature);
        }

        // validatorId有効性チェック
        {
            (bool success, string memory errTmp) = _validatorIdExistence.hasValidator(validatorId);
            require(success, errTmp);
        }
        _validatorData.modValidator(validatorId, name);

        emit ModValidator(validatorId, name, traceId);
    }

    /**
     * @dev アカウントの名前を更新する。バリデータの権限が必要。
     *
     * ```
     * emit event: ModAccount()
     * ```
     *
     * @param validatorId validatorId
     * @param accountId アカウントID
     * @param accountName アカウント名
     * @param traceId トレースID
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature バリデータによる署名
     */
    function modAccount(
        bytes32 validatorId,
        bytes32 accountId,
        string memory accountName,
        bytes32 traceId,
        uint256 deadline,
        bytes memory signature
    ) external override {
        // validatorの権限チェック
        {
            bytes32 hash = keccak256(abi.encode(validatorId, accountId, deadline));
            (bool success, string memory err) = this.hasValidatorRole(
                validatorId,
                hash,
                deadline,
                signature
            );
            require(success, err);
        }
        // validatorIdとの紐付けをチェック
        {
            // ValidatorとAccountの紐付き確認
            (bool success, string memory errTmp) = _hasAccount(validatorId, accountId);
            require(success, errTmp);
        }
        _contractManager.account().modAccount(accountId, accountName, traceId);
    }

    /**
     * @dev 指定されたValidatorIdに紐づくValidator情報を登録、もしくは上書きする
     *      バックアップリスト作業時のみ実行
     *
     * @param validator validator
     * @param deadline signatureのタイムスタンプ(秒)
     * @param signature Adminユーザによる署名
     */
    function setValidatorAll(
        ValidatorAll memory validator,
        uint256 deadline,
        bytes memory signature
    ) external {
        // Admin権限を持つかチェック
        {
            bytes32 hash = keccak256(abi.encode(_SET_VALIDATORS_ALL_SIGNATURE, deadline));
            _adminOnly(hash, deadline, signature);
        }

        _validatorIds.push(validator.validatorId);
        RemigrationLib.setValidatorAll(
            _validatorData[validator.validatorId],
            _accountIdExistenceByValidatorId[validator.validatorId],
            _validatorIdExistence,
            _issuerIdLinkedFlag,
            _contractManager,
            validator
        );
    }

    ///////////////////////////////////
    // call functions
    ///////////////////////////////////

    /**
     * @dev 検証者IDが登録済であるか確認する。
     *
     * @param validatorId validatorId
     * @return success true:登録済,false:未登録
     * @return err エラーメッセージ
     */
    function hasValidator(bytes32 validatorId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return _validatorIdExistence.hasValidator(validatorId);
    }

    /**
     * @dev Validatorに紐づくAccountの情報を取得する。
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return accountData アカウントデータ(zoneIdなし)
     * @return err エラーメッセージ
     */
    function getAccount(bytes32 validatorId, bytes32 accountId)
        external
        view
        override
        returns (AccountDataWithLimitData memory accountData, string memory err)
    {
        return ValidatorLib.getAccountData(_contractManager, validatorId, accountId);
    }

    /**
     * @dev 移転先のアカウント情報を取得する
     *
     * @param accountId アカウントID
     * @return accountName アカウント名
     * @return err エラー
     */
    function getDestinationAccount(bytes32 accountId)
        external
        view
        returns (string memory accountName, string memory err)
    {
        return _contractManager.account().getDestinationAccount(accountId);
    }

    /**
     * @dev バリデータが直接管理するアカウントIDを取得する
     *
     * @param validatorId バリデータID
     * @return accountId アカウントID
     * @return err エラー
     */
    function getValidatorAccountId(bytes32 validatorId)
        external
        view
        override
        returns (bytes32 accountId, string memory err)
    {
        return _validatorData.getValidatorAccountId(_contractManager, validatorId);
    }

    /**
     * @dev Validatorに紐づくAccountの全情報を取得する。(接続済みBusinessZoneAccount情報含む)
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return accountDataAll アカウントデータ(zoneIdあり)
     * @return err エラーメッセージ
     */
    function getAccountAll(bytes32 validatorId, bytes32 accountId)
        external
        view
        returns (AccountDataAll memory accountDataAll, string memory err)
    {
        bool success;
        (success, err) = _hasAccount(validatorId, accountId);
        if (!success) {
            return (accountDataAll, err);
        }
        (success, err) = _validatorIdExistence.hasValidator(validatorId);
        if (!success) {
            return (accountDataAll, err);
        }

        return (_contractManager.account().getAccountAll(accountId));
    }

    /**
     * @dev 検証者情報リストを取得する。
     *
     * @param limit limit
     * @param offset offset
     * @return validators validators
     * @return totalCount validatorの数
     * @return err エラーメッセージ
     */
    function getValidatorList(uint256 limit, uint256 offset)
        external
        view
        override
        returns (
            ValidatorListData[] memory validators,
            uint256 totalCount,
            string memory err
        )
    {
        return ValidatorLib.getValidatorList(_validatorData, _validatorIds, limit, offset);
    }

    /**
     * @dev アカウントに連携済みのzone情報一覧を取得する。(finZone含む)
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return zones zoneIdの配列
     * @return err エラーメッセージ
     */
    function getZoneByAccountId(bytes32 validatorId, bytes32 accountId)
        external
        view
        returns (ZoneData[] memory zones, string memory err)
    {
        (bool success, string memory errHasAccount) = _hasAccount(validatorId, accountId);
        if (!success) {
            return (new ZoneData[](0), errHasAccount);
        }

        (uint16 zoneId, , string memory errZone) = _contractManager.provider().getZone();
        require(bytes(errZone).length == 0, errZone);
        if (zoneId != _FINANCIAL_ZONE) {
            return (new ZoneData[](0), "");
        }

        zones = _contractManager.account().getZoneByAccountId(accountId);

        return (zones, "");
    }

    /**
     * @dev Validatorの情報を取得する。
     *
     * @param validatorId validatorId
     * @return name validatorの名前
     * @return issuerId validatorに紐づくissuerId
     * @return err エラーメッセージ
     */
    function getValidator(bytes32 validatorId)
        external
        view
        override
        returns (
            bytes32 name,
            bytes32 issuerId,
            string memory err
        )
    {
        return _validatorData.getValidator(_contractManager, validatorId);
    }

    /**
     * @dev 該当ValidatorIDに紐づくAccountの情報を取得する。
     *
     * @param validatorId validatorId
     * @param offset オフセット
     * @param limit リミット
     * @param sortOrder ソート順(desc: 降順, asc: 昇順)
     * @return accounts ValidatorAccountsData[]
     * @return totalCount
     * @return err エラーメッセージ
     */
    function getAccountList(
        bytes32 validatorId,
        uint256 offset,
        uint256 limit,
        string memory sortOrder
    )
        external
        view
        override
        returns (
            ValidatorAccountsData[] memory accounts,
            uint256 totalCount,
            string memory err
        )
    {
        bool success;
        (success, err) = _validatorIdExistence.hasValidator(validatorId);
        if (!success) {
            return (accounts, _EMPTY_LENGTH, err);
        }
        bytes32[] memory inAccountIds = ValidatorLib.getAccountIdList(
            _validatorData,
            validatorId,
            sortOrder
        );

        return ValidatorLib.getAccountList(_contractManager, inAccountIds, offset, limit);
    }

    /**
     * @dev 指定されたValidatorIDにAccountが紐付いているか確認を行う。
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return success true:紐づいている,false:紐づいていないい
     * @return err エラーメッセージ
     */
    function hasAccount(bytes32 validatorId, bytes32 accountId)
        external
        view
        override
        returns (bool success, string memory err)
    {
        return _hasAccount(validatorId, accountId);
    }

    /**
     * @dev 指定されたValidatorIDにAccountが紐付いているか確認を行う(内部関数)。
     *
     * @param validatorId validatorId
     * @param accountId accountId
     * @return success true:紐づいている,false:紐づいていないい
     * @return err エラーメッセージ
     */
    function _hasAccount(bytes32 validatorId, bytes32 accountId)
        internal
        view
        returns (bool success, string memory err)
    {
        // ValidatorID存在確認
        (success, err) = _validatorIdExistence.hasValidator(validatorId);
        if (!success) {
            return (success, err);
        }

        return _accountIdExistenceByValidatorId.hasAccount(validatorId, accountId);
    }

    /**
     * @dev Validatorの総数をカウントする。
     *
     * @return count Validatorの総数
     */
    function getValidatorCount() external view override returns (uint256 count) {
        return _validatorIds.length;
    }

    /**
     * @dev Indexに応じたValidatorIdを返す。
     *
     * @param index index
     * @return validatorId validatorId
     * @return err エラーメッセージ
     */
    function getValidatorId(uint256 index)
        external
        view
        override
        returns (bytes32 validatorId, string memory err)
    {
        if (_validatorIds.length <= index) {
            return (0x00, Error.VALIDATOR_OUT_OF_INDEX);
        }
        return (_validatorIds.getValidatorIds(index), "");
    }

    /**
     * @dev バリデータ権限を持っているか確認する。
     *
     * @param validatorId validatorId
     * @param hash hash
     * @param deadline deadline
     * @param signature signature
     */
    function hasValidatorRole(
        bytes32 validatorId,
        bytes32 hash,
        uint256 deadline,
        bytes memory signature
    ) external view returns (bool success, string memory err) {
        if (validatorId == 0x00) {
            return (false, Error.VALIDATOR_INVALID_VAL);
        }
        // 権限チェック
        (success, err) = _contractManager.accessCtrl().checkRole(
            _validatorData.getValidatorRole(validatorId),
            hash,
            deadline,
            signature
        );
    }

    /**
     * @dev バリデータ権限を持っているか確認する。
     *
     * @param validatorId validatorId
     * @param accountId accountId
     */
    function hasValidatorByAccount(bytes32 validatorId, bytes32 accountId)
        external
        view
        returns (bool success)
    {
        return (_accountIdExistenceByValidatorId[validatorId][accountId]);
    }

    /**
     * @dev バリデータ権限を持っているか確認する。
     *
     * @param index index
     */
    function getValidatorAll(uint256 index) external view returns (ValidatorAll memory validator) {
        bytes32 validatorId = _validatorIds[index];
        validator = RemigrationLib.getValidatorAll(
            _validatorData[validatorId],
            _accountIdExistenceByValidatorId[validatorId],
            _validatorIdExistence,
            _issuerIdLinkedFlag,
            _contractManager,
            validatorId
        );
        return validator;
    }

    ///////////////////////////////////
    // for upgrade contracts
    // deprecated
    ///////////////////////////////////

    // uint256[50] private __gap;
}
